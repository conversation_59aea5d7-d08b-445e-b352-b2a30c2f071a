import { Metadata } from 'next';

export const metadata: Metadata = {
    title: 'Help Wanted | Careers at Detail On The Go',
    description: 'Join the Detail On The Go team! View current job openings and career opportunities for mobile auto detailing professionals.',
    keywords: [
        'help wanted',
        'jobs',
        'careers',
        'auto detailing jobs',
        'mobile detailing jobs',
        'Detail On The Go jobs',
        'hiring',
        'employment'
    ],
    alternates: {
        canonical: 'https://detailongo.com/help-wanted/',
    },
    openGraph: {
        title: 'Help Wanted | Careers at Detail On The Go',
        description: 'Join the Detail On The Go team! View current job openings and career opportunities for mobile auto detailing professionals.',
        url: 'https://detailongo.com/help-wanted/',
        images: [
            {
                url: '/images/help-wanted-og.jpg',
                width: 1200,
                height: 630,
                alt: 'Help Wanted - Detail On The Go'
            }
        ],
        type: 'website',
    },
    twitter: {
        card: 'summary_large_image',
        title: 'Help Wanted | Careers at Detail On The Go',
        description: 'Join the Detail On The Go team! View current job openings and career opportunities for mobile auto detailing professionals.',
        images: ['/images/help-wanted-og.jpg'],
    }
};

export default function Layout({ children }: { children: React.ReactNode }) {
    return (
        <>
            {/* Structured Data for SEO */}
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "JobPosting",
                        "title": "Mobile Auto Detailing Professional",
                        "description": "Join the Detail On The Go team! View current job openings and career opportunities for mobile auto detailing professionals.",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "Detail On The Go",
                            "url": "https://detailongo.com/",
                            "image": "https://detailongo.com/images/help-wanted-og.jpg",
                            "telephone": "******-615-6156"
                        },
                        "employmentType": "FULL_TIME",
                        "industry": "Automotive Detailing",
                        "url": "https://detailongo.com/help-wanted/"
                    })
                }}
            />
            {children}
        </>
    );
}

"use client";
import Image from "next/image";
import Link from "next/link";
import { useState, useEffect } from "react";
import CleaningServiceRequest from "@/components/CleaningServiceRequest";

interface SlideshowProps {
  images: string[];
  index: number;
}

const Slideshow = ({ images, index }: SlideshowProps) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  useEffect(() => {
    const initialDelay = index * 500;
    const timer = setTimeout(() => {
      const interval = setInterval(() => {
        setCurrentImageIndex((prevIndex) => (prevIndex + 1) % images.length);
      }, 3000);
      return () => clearInterval(interval);
    }, initialDelay);

    return () => clearTimeout(timer);
  }, [images.length, index]);

  return (
    <div className="relative h-40 overflow-hidden">
      {images.map((src: string, imgIndex: number) => (
        <Image
          key={src}
          src={src}
          alt={`Slide ${imgIndex}`}
          fill
          className={`object-cover transition-opacity duration-1000 ease-in-out ${imgIndex === currentImageIndex ? "opacity-100" : "opacity-0"
            }`}
          style={{ position: "absolute" }}
        />
      ))}
    </div>
  );
};

export default function Home() {
  const [showBookingModal, setShowBookingModal] = useState(false);
  const [videoDelays, setVideoDelays] = useState<number[]>([]);

  useEffect(() => {
    const delays = ['Car', 'RV', 'Boat'].map(() => Math.floor(Math.random() * 5000));
    setVideoDelays(delays);
  }, []);

  return (
    <main id="main-content" className="flex flex-col items-center min-h-screen space-y-8 font-sans">

      {/* Service Request Modal */}
      {showBookingModal && (
        <CleaningServiceRequest onClose={() => setShowBookingModal(false)} />
      )}

      {/* Hero Section */}
      <section className="w-full py-5 md:pt-16 md:pb-4">
        <div className="max-w-6xl mx-auto px-2">
          <div className="flex justify-center">
            {/* Text Content */}
            <div className="text-center">
              <div className="mb-6">
                <h2 className="text-2xl md:text-3xl font-thin text-white tracking-tighter mb-2">
                  Consistent • Reliable • Trusted
                </h2>
                <h1 className="mb-2 text-5xl md:text-6xl font-bold text-white tracking-tighter">
                  Cleaning Services
                </h1>
              </div>
              <p className="text-lg text-white/90 mb-6">
                Our team here at Peak Services offers cleaning services in Ketchikan Alaska, and all of S.E.
              </p>
              {/* Modified button container */}
              <div className="flex justify-center px-8 lg:px-0">
                <div className="relative">
                  {/* Added z-index-30 to ensure the emoji is on top */}
                  <span className="absolute -left-10 top-1/2 -translate-y-1/2 text-4xl z-20">
                    <span className="inline-block wave-animation">👉</span>
                  </span>
                  <button
                    onClick={() => setShowBookingModal(true)}
                    className="group relative text-black px-6 py-3 rounded-lg font-semibold transition-all transform hover:scale-105 hover:translate-y-[-5px] hover:shadow-lg shiny-button border border-white"
                    style={{ backgroundColor: '#dfff69' }}
                  >
                    <span className="relative z-10 italic font-bold text-xl tracking-tighter">
                      REQUEST YOUR CLEANING SERVICE
                    </span>
                    <span
                      className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"
                      style={{ background: 'linear-gradient(to right, #dfff69, #c9e85a)' }}
                    ></span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-7 px-12 mb-8 border-2 border-white rounded-xl w-fit mx-auto" style={{ backgroundColor: "#ffffffff" }}>
        <div className="flex justify-start mb-6">
          <h2 className="font-bold text-3xl md:text-5xl tracking-tighter" style={{ color: '#0066ff' }}>
            We specialize in:
          </h2>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 justify-items-center">
          {cleaningServices.map((service, idx) => (
            <div
              key={service.name}
              className="group overflow-hidden relative rounded-xl w-80 h-64"
              style={{ backgroundColor: "rgba(15, 55, 255, 0.9)" }}
            >
              <div className="relative h-40 overflow-hidden rounded-t-xl">
                <Image
                  src={service.image}
                  alt={service.name}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                <div className="absolute top-2 right-2 text-2xl bg-white/20 backdrop-blur-sm rounded-full p-2">
                  {service.icon}
                </div>
              </div>
              <div className="p-4 text-center">
                <h3 className="text-lg font-semibold text-white text-center tracking-tighter">
                  {service.name}
                </h3>
              </div>
            </div>
          ))}
        </div>

        {/* Special Announcement */}
        <div className="mt-8 p-6 bg-gradient-to-r from-green-500 to-green-600 rounded-xl border-2 border-white">
          <div className="text-center">
            <h3 className="text-xl font-bold text-white mb-2 tracking-tighter">🌟 New Service Alert!</h3>
            <p className="text-white/90">
              As of June 2024 we are happy to announce we offer grounds keeping (e.g weeding, trash pick up, pruning, fertilizing, etc)
            </p>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-7 px-12 mb-8 border-2 border-white rounded-xl w-fit mx-auto" style={{ backgroundColor: "#000edf" }}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="p-4 rounded-xl border border-white/30" style={{ backgroundColor: "#000edf" }}>
            <h3 className="text-2xl font-bold mb-4 text-white tracking-tighter">Mission</h3>
            <p className="text-white/90">
              We pride ourselves in our mission to help keep your space clean, whether in the office or at home. We think it's important for you to feel happy in your space wherever you are.
            </p>
          </div>
          <div className="p-4 rounded-xl border border-white/30" style={{ backgroundColor: "#000edf" }}>
            <h3 className="text-2xl font-bold mb-4 text-white tracking-tighter">Vision</h3>
            <p className="text-white/90">
              Striving to be a leading provider in Southeast Alaska for industry leading cleaning services for commercial and residential spaces, floor finish maintenance, and Move in/Move-out cleanings.
            </p>
          </div>
        </div>
      </section>

      {/* Customer Testimonials */}
      <section className="w-full py-16 px-2 mb-8">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl md:text-5xl font-bold text-white text-center mb-12 tracking-tighter">
            What Our Customers Say
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                name: "Sarah M.",
                location: "Ketchikan, AK",
                text: "Peak Services has been cleaning our office for over a year now. Megan and her team are incredibly reliable and thorough. Our workspace has never looked better!",
                rating: 5
              },
              {
                name: "Mike & Jennifer T.",
                location: "Southeast Alaska",
                text: "We use Peak Services for our vacation rental turnarounds. They're always on time, professional, and our guests consistently comment on how clean the property is.",
                rating: 5
              },
              {
                name: "Alaska Marine Services",
                location: "Ketchikan, AK",
                text: "Peak Services handles all our commercial cleaning needs. From our offices to our warehouse, they maintain the highest standards. Highly recommend!",
                rating: 5
              }
            ].map((testimonial, index) => (
              <div key={index} className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <div className="flex mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <span key={i} className="text-yellow-400 text-xl">⭐</span>
                  ))}
                </div>
                <p className="text-white/90 mb-4 italic">"{testimonial.text}"</p>
                <div className="text-white font-semibold">{testimonial.name}</div>
                <div className="text-white/70 text-sm">{testimonial.location}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Happiness Guarantee */}
      <section className="py-7 px-12 mb-8 border-2 border-white rounded-xl w-fit mx-auto" style={{ backgroundColor: "#000edf" }}>
        <div className="text-center">
          <h2 className="text-white font-bold text-3xl md:text-5xl tracking-tighter mb-6">
            "Happiness Guarantee"
          </h2>
          <div className="max-w-4xl mx-auto">
            <p className="text-white/90 text-lg leading-relaxed">
              We want you to be delighted with our service! If you are dissatisfied with any of our cleaning services, please contact us RIGHT AWAY (within 24 hrs of service) so that we can try to resolve the issue. We will first assess the situation to make sure that we understand the concern. Then, we will take appropriate action to re-clean the problem area at no additional cost to you.
            </p>
          </div>
        </div>
      </section>

      {/* Contact CTA */}
      <section className="w-full py-16 px-2 mb-8">
        <div className="max-w-4xl mx-auto px-2">
          <div className="bg-gradient-to-br from-blue-500 to-blue-600 text-white py-12 px-8 rounded-lg text-center">
            <h2 className="text-4xl font-bold mb-6 tracking-tighter">
              Ready for a Clean Space?
            </h2>
            <p className="text-xl mb-8 max-w-2xl mx-auto text-white/90">
              Contact Peak Services today for professional cleaning services in Ketchikan Alaska and all of S.E.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <a
                href="tel:+19078211335"
                className="group relative bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-all transform hover:scale-105 hover:-translate-y-[2px] hover:shadow-lg"
              >
                <span className="relative z-10 font-bold text-lg tracking-tighter">
                  📞 (*************
                </span>
              </a>
              <a
                href="mailto:<EMAIL>"
                className="group relative bg-pink-500 text-white px-8 py-4 rounded-lg font-semibold hover:bg-pink-600 transition-all transform hover:scale-105 hover:-translate-y-[2px] hover:shadow-lg"
              >
                <span className="relative z-10 font-bold text-lg tracking-tighter">
                  ✉️ Email Us
                </span>
              </a>
            </div>
          </div>
        </div>
        <style jsx>{`
                .wave-animation {
                    animation: wave 1.5s infinite;
                }
                @keyframes wave {
                    0%, 100% { transform: translateX(0); }
                    50% { transform: translateX(10px); }
                }
            `}</style>
      </section>

    </main>
  );
}

// Cleaning services data
const cleaningServices = [
  {
    name: "Residential cleaning services",
    icon: "🏠",
    image: "/residential couch cleaning.png"
  },
  {
    name: "Commercial cleaning services",
    icon: "🏢",
    image: "/office cleaning.png"
  },
  {
    name: "Carpet shampooing",
    icon: "🧽",
    image: "/carpet cleaning.png"
  },
  {
    name: "Window washing",
    icon: "🪟",
    image: "/commercial window cleaning.png"
  },
  {
    name: "Vacation rental turn-arounds",
    icon: "🏖️",
    image: "/residential bedroom cleaning.png"
  },
  {
    name: "Move-in/Move-out cleanings",
    icon: "📦",
    image: "/residential couch cleaning 2.png"
  },
  {
    name: "Dryer vent cleaning",
    icon: "🌪️",
    image: "/peak services employee putting on hardhat.png"
  },
  {
    name: "Pressure washing",
    icon: "💧",
    image: "/commercial floor cleaning.png"
  },
  {
    name: "Post construction clean-up",
    icon: "🔨",
    image: "/commercial floor cleaning 2.png"
  },
  {
    name: "Industrial area clean up",
    icon: "🏭",
    image: "/commercial bathroom janitorial.png"
  },
  {
    name: "Grounds keeping (weeding, trash pick up, pruning, fertilizing, etc)",
    icon: "🌱",
    image: "/floor cleaning.png"
  }
];
"use client";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState, useRef } from "react";
import useScroll from "@/lib/hooks/use-scroll";
import { auth, db } from "@/lib/firebase/firebase";
import { useAuthState } from "react-firebase-hooks/auth";
import { useDocument } from "react-firebase-hooks/firestore";
import { doc } from "firebase/firestore";
import CleaningServiceRequest from "@/components/CleaningServiceRequest";
import { useRouter, usePathname } from "next/navigation";

export default function NavBar() {
  const scrolled = useScroll(50);
  const [user, loading] = useAuthState(auth);
  const [mounted, setMounted] = useState(false);
  const router = useRouter();
  const [userDoc, userDocLoading] = useDocument(
    user ? doc(db, "users", user.uid) : null
  );
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [mobileMoreOpen, setMobileMoreOpen] = useState(false);
  const [showBookingModal, setShowBookingModal] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const pathname = usePathname();

  useEffect(() => {
    setMounted(true);
  }, []);

  // Only close menu when scrolling, don't prevent opening
  useEffect(() => {
    const handleScroll = () => {
      if (mobileMenuOpen) {
        setMobileMenuOpen(false);
        setMobileMoreOpen(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [mobileMenuOpen]);

  // Handle click outside to close mobile menu
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        mobileMenuOpen &&
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setMobileMenuOpen(false);
        setMobileMoreOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [mobileMenuOpen]);

  const closeMobileMenu = () => {
    setMobileMenuOpen(false);
    setMobileMoreOpen(false);
  };


  // Apply blurry background either when scrolled OR when mobile menu is open
  const navbarBgClass = scrolled || mobileMenuOpen
    ? "border-b bg-white/50 backdrop-blur-xl"
    : "bg-white/0";


  const [ignoreHover, setIgnoreHover] = useState(false);

  // Update your toggleMoreMenu function
  const toggleMoreMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    setIgnoreHover(true);
    setMobileMoreOpen((prev) => !prev);
    setTimeout(() => setIgnoreHover(false), 300);
  };

  return (
    <>
      <div
        className={`fixed top-0 flex w-full justify-center font-sans ${navbarBgClass} z-30 transition-all`}
      >
        <div className="mx-5 flex h-20 w-full max-w-screen-xl items-center justify-between">
          <Link
            href="/"
            className="flex items-center font-sans text-2xl justify-start"
          >
            <Image
              src="/character white.png"
              alt="Peak Services Logo"
              width={100}
              height={200}
              className="rounded-sm text-2xl"
            />
            <span className={`font-bold text-2xl ${scrolled || mobileMenuOpen ? "text-gray-800" : "text-white"} transition-colors`}>
              Peak Services
            </span>
          </Link>


          <a
            href="#main-content"
            className="sr-only focus:not-sr-only focus:absolute focus:p-2 focus:bg-blue-700 focus:text-white font-sans"
          >
            Skip to Main Content
          </a>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-6 font-sans">
            <button
              onClick={() => setShowBookingModal(true)}
              className="font-semibold bg-white text-blue-900 px-4 py-2 rounded-lg shadow-md hover:bg-gray-100 transition-colors font-sans"
            >
              Request →
            </button>
            <Link
              href="/services"
              className={`font-semibold ${scrolled || mobileMenuOpen ? "text-gray-800" : ""
                } hover:bg-pink-500 hover:text-white hover:text-lg hover:font-bold p-2 rounded transition-all font-sans`}
            >
              Services
            </Link>
            <Link
              href="/faq"
              className={`font-semibold ${scrolled || mobileMenuOpen ? "text-gray-800" : ""
                } hover:bg-pink-500 hover:text-white hover:text-lg hover:font-bold p-2 rounded transition-all font-sans`}
            >
              FAQ
            </Link>
            <Link
              href="/about"
              className={`font-semibold ${scrolled || mobileMenuOpen ? "text-gray-800" : ""
                } hover:bg-pink-500 hover:text-white hover:text-lg hover:font-bold p-2 rounded transition-all font-sans`}
            >
              About
            </Link>
          </div>

          {/* Mobile menu button */}
          <button
            ref={buttonRef}
            className="md:hidden flex items-center font-sans"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            aria-label="Toggle mobile menu"
            aria-expanded={mobileMenuOpen}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d={
                  mobileMenuOpen
                    ? "M6 18L18 6M6 6l12 12"
                    : "M4 6h16M4 12h16M4 18h16"
                }
              />
            </svg>
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      <div
        ref={menuRef}
        className={`md:hidden fixed top-20 inset-x-0 bg-white/50 backdrop-blur-xl shadow-md py-3 px-6 z-20 transition-all duration-300 font-sans ${mobileMenuOpen
          ? "opacity-100 translate-y-0"
          : "opacity-0 -translate-y-2 pointer-events-none"
          }`}
      >
        <button
          onClick={() => {
            setShowBookingModal(true);
            closeMobileMenu();
          }}
          className="block w-full py-2 font-semibold bg-white text-blue-900 text-left px-4 rounded-lg shadow-md hover:bg-gray-100 my-2 text-3xl font-sans"
        >
          Request →
        </button>
        <Link
          href="/services"
          onClick={closeMobileMenu}
          className={`block py-2 px-2 my-1 font-semibold rounded font-sans ${pathname === "/services"
            ? "bg-pink-500 text-white text-3xl font-bold"
            : "text-gray-800 text-3xl"
            } hover:bg-pink-500 hover:text-white hover:font-bold`}
        >
          Services
        </Link>
        <Link
          href="/faq"
          onClick={closeMobileMenu}
          className={`block py-2 px-2 my-1 font-semibold rounded font-sans ${pathname === "/faq"
            ? "bg-pink-500 text-white text-3xl font-bold"
            : "text-gray-800 text-3xl"
            } hover:bg-pink-500 hover:text-white hover:font-bold`}
        >
          F.A.Q.
        </Link>
        <Link
          href="/about"
          onClick={closeMobileMenu}
          className={`block py-2 px-2 my-1 font-semibold rounded font-sans ${pathname === "/about"
            ? "bg-pink-500 text-white text-3xl font-bold"
            : "text-gray-800 text-3xl"
            } hover:bg-pink-500 hover:text-white hover:font-bold`}
        >
          About
        </Link>

      </div>

      {/* Service Request Modal */}
      {showBookingModal && (
        <CleaningServiceRequest onClose={() => setShowBookingModal(false)} />
      )}
    </>
  );
}

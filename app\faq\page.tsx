"use client";

import { helvetica } from "../fonts";
import cx from "classnames";
import { useState } from "react";
import CleaningServiceRequest from "@/components/CleaningServiceRequest";

export default function FAQ() {
    const [showBookingModal, setShowBookingModal] = useState(false);
    return (
        <div
            className={cx(
                helvetica.variable,
                "pt-20 flex flex-col items-center min-h-screen space-y-8 p-4 sm:p-8 bg-transparent text-gray-100 font-sans"
            )}
        >
            {/* Service Request Modal */}
            {showBookingModal && (
                <CleaningServiceRequest onClose={() => setShowBookingModal(false)} />
            )}
            {/* Header */}
            <div className="text-center py-10 px-6">
                <h1 className="text-4xl md:text-6xl font-bold text-white tracking-tighter">
                    F.A.Q.
                </h1>
                <h3 className="mt-6 text-lg text-white/90 md:text-xl font-medium">
                    Frequently Asked Questions about Peak Services AK
                </h3>
            </div>

            {/* FAQ Sections */}
            <div className="w-full max-w-6xl mx-auto mt-10 space-y-6 bg-[#1e92f8] backdrop-blur-sm rounded-xl p-6">

                {/* Getting Started Section */}
                <div className="mb-8">
                    <h2 className="text-2xl font-bold text-white mb-4">Getting Started</h2>
                </div>

                {/* How to book */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-medium text-white tracking-tighter">
                        How do I book a cleaning service? 📅
                        <span className="text-white group-open:hidden">+</span>
                        <span className="text-white hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-white min-h-32">
                        <p>
                            You can request a cleaning service by filling out our{" "}
                            <button
                                onClick={() => setShowBookingModal(true)}
                                className="text-white hover:underline cursor-pointer bg-transparent border-none p-0 font-inherit"
                            >
                                online form
                            </button>
                            , calling us directly at (*************, or sending us an <NAME_EMAIL>. We'll contact you within 24 hours to discuss your needs and provide a free estimate.
                        </p>
                    </div>
                </details>

                {/* Service Areas */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-medium text-white tracking-tighter">
                        What areas do you serve? 🗺️
                        <span className="text-white group-open:hidden">+</span>
                        <span className="text-white hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-white min-h-32">
                        <p>
                            We provide cleaning services throughout Ketchikan Alaska and all of S.E. Contact us to confirm service availability in your specific location.
                        </p>
                    </div>
                </details>

                {/* Booking advance notice */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-medium text-white tracking-tighter">
                        How far in advance do I need to book? ⏰
                        <span className="text-white group-open:hidden">+</span>
                        <span className="text-white hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-white min-h-32">
                        <p>
                            We recommend booking at least 48-72 hours in advance to ensure availability, especially during busy seasons. However, we'll do our best to accommodate last-minute requests when possible.
                        </p>
                    </div>
                </details>

                {/* Free estimates */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-medium text-white tracking-tighter">
                        Do you offer free estimates? 💰
                        <span className="text-white group-open:hidden">+</span>
                        <span className="text-white hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-white min-h-32">
                        <p>
                            Yes! We provide free estimates for all our services. We'll assess your space and cleaning needs to give you an accurate quote with no obligation.
                        </p>
                    </div>
                </details>

                {/* Service Details Section */}
                <div className="mb-8 mt-12">
                    <h2 className="text-2xl font-bold text-white mb-4">Service Details</h2>
                </div>

                {/* Standard cleaning */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-medium text-white tracking-tighter">
                        What's included in a standard cleaning? 🧽
                        <span className="text-white group-open:hidden">+</span>
                        <span className="text-white hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-white min-h-32">
                        <p className="mb-2">Our standard cleaning includes:</p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                            <li>Dusting all surfaces</li>
                            <li>Vacuuming carpets and mopping floors</li>
                            <li>Cleaning and sanitizing bathrooms</li>
                            <li>Kitchen cleaning (counters, appliances, sink)</li>
                            <li>Trash removal and general tidying</li>
                        </ul>
                    </div>
                </details>

                {/* Cleaning time */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-medium text-white tracking-tighter">
                        How long does a typical cleaning take? ⏱️
                        <span className="text-white group-open:hidden">+</span>
                        <span className="text-white hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-white min-h-32">
                        <p>
                            Cleaning time varies based on the size of your space and services requested. A standard home cleaning typically takes 2-4 hours, while commercial spaces vary significantly based on square footage and requirements.
                        </p>
                    </div>
                </details>

                {/* Being home during cleaning */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-medium text-white tracking-tighter">
                        Do I need to be home during the cleaning? 🏠
                        <span className="text-white group-open:hidden">+</span>
                        <span className="text-white hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-white min-h-32">
                        <p>
                            No, you don't need to be home. Many of our clients prefer to leave us a key or provide access instructions. Our insured and bonded technicians will secure your property when the cleaning is complete.
                        </p>
                    </div>
                </details>

                {/* Preparation */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-medium text-white tracking-tighter">
                        What should I do to prepare for the cleaning? 📋
                        <span className="text-white group-open:hidden">+</span>
                        <span className="text-white hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-white min-h-32">
                        <p className="mb-2">To help us work efficiently:</p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                            <li>Clear clutter from surfaces and floors</li>
                            <li>Secure valuable or fragile items</li>
                            <li>Ensure pets are crated or removed from the premises</li>
                            <li>Provide access to all areas you want cleaned</li>
                            <li>Leave any special instructions</li>
                        </ul>
                    </div>
                </details>

                {/* Supplies and equipment */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-medium text-white tracking-tighter">
                        Do you bring your own supplies and equipment? 🧴
                        <span className="text-white group-open:hidden">+</span>
                        <span className="text-white hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-white min-h-32">
                        <p>
                            Yes, we bring all necessary cleaning supplies and equipment. If you have specific product preferences or allergies, please let us know and we can accommodate your needs.
                        </p>
                    </div>
                </details>

                {/* Pricing & Payment Section */}
                <div className="mb-8 mt-12">
                    <h2 className="text-2xl font-bold text-white mb-4">Pricing & Payment</h2>
                </div>

                {/* Pricing determination */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-medium text-white tracking-tighter">
                        How do you determine pricing? 💰
                        <span className="text-white group-open:hidden">+</span>
                        <span className="text-white hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-white min-h-32">
                        <p className="mb-2">Our pricing is customized based on:</p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                            <li>Size and type of property</li>
                            <li>Services requested</li>
                            <li>Frequency of cleaning</li>
                            <li>Specific requirements or challenges</li>
                        </ul>
                        <p className="mt-2">We believe in transparent, fair pricing and will work within your budget when possible.</p>
                    </div>
                </details>

                {/* Regular cleaning rates */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        Do you offer different rates for regular cleaning? 🔄
                        <span className="text-white group-open:hidden">+</span>
                        <span className="text-white hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-white min-h-32">
                        <p>
                            Yes! We offer discounted rates for recurring services (weekly, bi-weekly, or monthly cleaning). Regular customers receive priority scheduling and special pricing.
                        </p>
                    </div>
                </details>

                {/* Payment methods */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        What forms of payment do you accept? 💳
                        <span className="text-white group-open:hidden">+</span>
                        <span className="text-white hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-white min-h-32">
                        <p>
                            We accept cash, check, and electronic payments. Payment is typically due upon completion of service unless other arrangements have been made.
                        </p>
                    </div>
                </details>

                {/* Budget accommodation */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        Can you work within my budget? 💵
                        <span className="text-white group-open:hidden">+</span>
                        <span className="text-white hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-white min-h-32">
                        <p>
                            Absolutely. We encourage customers to share their budget with us so we can prioritize services and create a cleaning plan that works for you.
                        </p>
                    </div>
                </details>

                {/* Specific Services & Pricing Section */}
                <div className="mb-8 mt-12">
                    <h2 className="text-2xl font-bold text-white mb-4">Specific Services & Pricing</h2>
                </div>

                {/* Vacation rentals */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        Do you clean vacation rentals? 🏖️
                        <span className="text-white group-open:hidden">+</span>
                        <span className="text-white hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-white min-h-32">
                        <p>
                            Yes! We offer specialized vacation rental turnaround cleaning with flat-rate pricing based on your checkout requirements. This ensures consistent, reliable service for your guests.
                        </p>
                    </div>
                </details>

                {/* Carpet cleaning */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        What about carpet cleaning? 🧽
                        <span className="text-white group-open:hidden">+</span>
                        <span className="text-white hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-white min-h-32">
                        <p className="mb-2"><strong>Residential:</strong> $130 minimum (2-hour minimum at hourly rate). Additional charges may apply for excessive stain treatment.</p>
                        <p><strong>Commercial:</strong> Pricing determined after walkthrough, considering square footage, furniture moving, stain severity, stairs, and facility access.</p>
                    </div>
                </details>

                {/* Window washing */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        Do you offer window washing? 🪟
                        <span className="text-white group-open:hidden">+</span>
                        <span className="text-white hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-white min-h-32">
                        <p>
                            Yes, we provide interior and exterior window washing. Pricing varies based on number of windows, accessibility, and special requirements (ladders, decorative glass, etc.).
                        </p>
                    </div>
                </details>

                {/* Commercial services */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        What commercial services do you provide? 🏢
                        <span className="text-white group-open:hidden">+</span>
                        <span className="text-white hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-white min-h-32">
                        <p className="mb-2">We offer comprehensive commercial cleaning including:</p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                            <li>Regular office cleaning</li>
                            <li>Floor stripping and waxing</li>
                            <li>Carpet shampooing</li>
                            <li>Post-construction cleanup</li>
                            <li>Customized maintenance contracts</li>
                        </ul>
                        <p className="mt-2">Commercial pricing is provided after a walkthrough and includes detailed contracts specifying duties, pricing, and schedules.</p>
                    </div>
                </details>

                {/* Policies & Important Information Section */}
                <div className="mb-8 mt-12">
                    <h2 className="text-2xl font-bold text-white mb-4">Policies & Important Information</h2>
                </div>

                {/* Cancellation policy */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        What's your cancellation policy? �
                        <span className="text-white group-open:hidden">+</span>
                        <span className="text-white hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-white min-h-32">
                        <p>
                            We require 48 hours notice for cancellations or rescheduling. Cancellations made with less than 48 hours notice incur a $100 fee. This policy protects our technicians' scheduled income and helps us maintain reliable service for all customers.
                        </p>
                    </div>
                </details>

                {/* Satisfaction guarantee */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        What if I'm not satisfied with the cleaning? 😥
                        <span className="text-white group-open:hidden">+</span>
                        <span className="text-white hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-white min-h-32">
                        <p className="mb-2">We guarantee your satisfaction! If you're not completely happy with our service, contact us within 24 hours and we'll:</p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                            <li>Assess the situation to understand your concerns</li>
                            <li>Return to re-clean any problem areas at no additional cost</li>
                            <li>Work with you to ensure the issue is resolved</li>
                        </ul>
                    </div>
                </details>

                {/* Tipping */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        Do I need to tip the cleaning staff? 💵
                        <span className="text-white group-open:hidden">+</span>
                        <span className="text-white hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-white min-h-32">
                        <p>
                            Tipping is never required but is always appreciated as a way to show your cleaner they did an excellent job. You can leave cash in a labeled envelope or use our convenient digital payment options.
                        </p>
                    </div>
                </details>

                {/* Pets during cleaning */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        What about pets during cleaning? 🐕🐱
                        <span className="text-white group-open:hidden">+</span>
                        <span className="text-white hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-white min-h-32">
                        <p>
                            For the safety of both our staff and your pets, we ask that animals be crated or off-premises during cleaning. If we've met your pet previously and they're comfortable with our team, we may allow them to remain uncrated in a separate area.
                        </p>
                    </div>
                </details>

                {/* Children at home */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        What if I have children at home? �
                        <span className="text-white group-open:hidden">+</span>
                        <span className="text-white hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-white min-h-32">
                        <p>
                            We ask that children not be left unsupervised during cleaning sessions. Our technicians need to focus on their work and use cleaning chemicals that require adult supervision around children.
                        </p>
                    </div>
                </details>

                {/* Insurance and bonding */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        Are your employees insured and bonded? 🛡️
                        <span className="text-white group-open:hidden">+</span>
                        <span className="text-white hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-white min-h-32">
                        <p>
                            Yes, all our cleaning technicians are fully insured and bonded for your protection and peace of mind. Alaska Business License #: 2164310.
                        </p>
                    </div>
                </details>

                {/* Contact & Support Section */}
                <div className="mb-8 mt-12">
                    <h2 className="text-2xl font-bold text-white mb-4">Contact & Support</h2>
                </div>

                {/* Questions not covered */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        What if I have questions not covered here? ❓
                        <span className="text-white group-open:hidden">+</span>
                        <span className="text-white hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-white min-h-32">
                        <p className="mb-2">We're here to help! Contact us by:</p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                            <li><strong>Phone:</strong> (*************</li>
                            <li><strong>Email:</strong> <EMAIL></li>
                            <li><strong>Online:</strong> Submit a question through our{" "}
                                <button
                                    onClick={() => setShowBookingModal(true)}
                                    className="text-white hover:underline cursor-pointer bg-transparent border-none p-0 font-inherit"
                                >
                                    contact form
                                </button>
                            </li>
                        </ul>
                    </div>
                </details>

                {/* Response time */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        How quickly do you respond to inquiries? ⏰
                        <span className="text-white group-open:hidden">+</span>
                        <span className="text-white hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-white min-h-32">
                        <p>
                            We typically respond to all inquiries within 24 hours, and often much sooner. For urgent cleaning needs or questions, please call us directly.
                        </p>
                    </div>
                </details>

                {/* Emergency services */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        Do you offer emergency cleaning services? 🚨
                        <span className="text-white group-open:hidden">+</span>
                        <span className="text-white hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-white min-h-32">
                        <p>
                            While we can't guarantee same-day service, we'll do our best to accommodate emergency situations. Additional fees may apply for rush services.
                        </p>
                    </div>
                </details>

                {/* CTA Section */}
                <div className="mt-12 bg-[rgba(15,55,255,0.9)] backdrop-blur-sm p-8 rounded-xl text-center min-h-32">
                    <h2 className="text-2xl font-black text-white tracking-tighter">Have a question not answered here?</h2>
                    <p className="mt-4 text-white">We'd love to hear from you! Contact Peak Services AK today and let us know how we can help make your space spotless.</p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mt-6">
                        <a
                            href="tel:+19078211335"
                            className="bg-pink-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-pink-600 transition-colors tracking-tighter"
                        >
                            📞 Call (*************
                        </a>
                        <button
                            onClick={() => setShowBookingModal(true)}
                            className="group relative text-black px-6 py-3 rounded-lg font-semibold transition-all transform hover:scale-105 hover:translate-y-[-2px] hover:shadow-lg shiny-button border border-white tracking-tighter"
                            style={{ backgroundColor: '#dfff69' }}
                        >
                            <span className="relative z-10 font-bold">
                                📝 Request Service
                            </span>
                            <span
                                className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"
                                style={{ background: 'linear-gradient(to right, #dfff69, #c9e85a)' }}
                            ></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}